'use client';

import { useState, useEffect } from 'react';
import { GitCompare, X, ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { VersionDiff, FieldChange } from '@/lib/types/versioning';

interface VersionComparisonProps {
  isOpen: boolean;
  onClose: () => void;
  toolId: string;
  fromVersion: number;
  toVersion: number;
}

export function VersionComparison({ 
  isOpen, 
  onClose, 
  toolId, 
  fromVersion, 
  toVersion 
}: VersionComparisonProps) {
  const [comparison, setComparison] = useState<VersionDiff | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [summary, setSummary] = useState<string>('');

  useEffect(() => {
    if (isOpen && fromVersion && toVersion) {
      fetchComparison();
    } else {
      setComparison(null);
      setError(null);
      setSummary('');
    }
  }, [isOpen, toolId, fromVersion, toVersion]);

  const fetchComparison = async () => {
    setLoading(true);
    setError(null);

    try {
      const adminApiKey = 'aidude_admin_2024_secure_key_xyz789'; // This should be passed as a prop in production
      
      const response = await fetch(
        `/api/admin/tools/${toolId}/versions/compare?fromVersion=${fromVersion}&toVersion=${toVersion}&includeMetadata=true`,
        {
          headers: {
            'x-api-key': adminApiKey,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to compare versions');
      }

      const result = await response.json();
      
      if (result.success) {
        setComparison(result.data.comparison);
        setSummary(result.data.summary);
      } else {
        throw new Error(result.error || 'Comparison failed');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Comparison failed');
    } finally {
      setLoading(false);
    }
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getChangeTypeColor = (type: 'added' | 'modified' | 'removed'): string => {
    switch (type) {
      case 'added':
        return 'text-green-400 bg-green-500/10 border-green-500/20';
      case 'modified':
        return 'text-blue-400 bg-blue-500/10 border-blue-500/20';
      case 'removed':
        return 'text-red-400 bg-red-500/10 border-red-500/20';
      default:
        return 'text-gray-400 bg-gray-500/10 border-gray-500/20';
    }
  };

  const getSignificanceColor = (significance: 'low' | 'medium' | 'high'): string => {
    switch (significance) {
      case 'high':
        return 'text-red-400';
      case 'medium':
        return 'text-orange-400';
      case 'low':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) {
      return 'null';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const groupChangesByField = (changes: FieldChange[]): Record<string, FieldChange[]> => {
    return changes.reduce((acc, change) => {
      const topLevelField = change.path.split('.')[0];
      if (!acc[topLevelField]) {
        acc[topLevelField] = [];
      }
      acc[topLevelField].push(change);
      return acc;
    }, {} as Record<string, FieldChange[]>);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-zinc-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-zinc-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-full">
              <GitCompare className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">
                Version Comparison
              </h2>
              <p className="text-sm text-gray-400">
                Comparing version {fromVersion} with version {toVersion}
              </p>
            </div>
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            size="sm"
            className="text-gray-400 hover:text-white"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-gray-400">Comparing versions...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-400 mb-4">Error: {error}</p>
              <Button onClick={fetchComparison} variant="outline">
                Retry
              </Button>
            </div>
          ) : comparison ? (
            <div className="space-y-6">
              {/* Summary */}
              <div className="bg-zinc-700 rounded-lg p-4">
                <h3 className="font-medium text-white mb-3">Comparison Summary</h3>
                <p className="text-gray-300 mb-4">{summary}</p>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{comparison.summary.totalChanges}</div>
                    <div className="text-sm text-gray-400">Total Changes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{comparison.summary.addedFields}</div>
                    <div className="text-sm text-gray-400">Added</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">{comparison.summary.modifiedFields}</div>
                    <div className="text-sm text-gray-400">Modified</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-400">{comparison.summary.removedFields}</div>
                    <div className="text-sm text-gray-400">Removed</div>
                  </div>
                </div>

                {comparison.summary.significantChanges.length > 0 && (
                  <div className="mt-4 p-3 bg-orange-500/10 border border-orange-500/20 rounded">
                    <p className="text-orange-300 font-medium text-sm mb-2">Significant Changes:</p>
                    <ul className="list-disc list-inside space-y-1">
                      {comparison.summary.significantChanges.map((change, index) => (
                        <li key={index} className="text-orange-400 text-sm">{change}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Changes by Field */}
              {comparison.changes.length > 0 ? (
                <div className="space-y-4">
                  <h3 className="font-medium text-white">Detailed Changes</h3>
                  
                  {Object.entries(groupChangesByField(comparison.changes)).map(([fieldName, fieldChanges]) => (
                    <div key={fieldName} className="border border-zinc-700 rounded-lg">
                      <button
                        onClick={() => toggleSection(fieldName)}
                        className="w-full flex items-center justify-between p-4 text-left hover:bg-zinc-700/50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          {expandedSections[fieldName] ? (
                            <ChevronDown className="w-4 h-4 text-gray-400" />
                          ) : (
                            <ChevronRight className="w-4 h-4 text-gray-400" />
                          )}
                          <span className="font-medium text-white">{fieldName}</span>
                          <span className="text-sm text-gray-400">
                            ({fieldChanges.length} change{fieldChanges.length !== 1 ? 's' : ''})
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {fieldChanges.map((change, index) => (
                            <span
                              key={index}
                              className={`px-2 py-1 text-xs rounded-full border ${getChangeTypeColor(change.type)}`}
                            >
                              {change.type}
                            </span>
                          ))}
                        </div>
                      </button>

                      {expandedSections[fieldName] && (
                        <div className="border-t border-zinc-700 p-4 space-y-3">
                          {fieldChanges.map((change, index) => (
                            <div key={index} className="bg-zinc-800 rounded p-3">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-white">{change.path}</span>
                                <div className="flex items-center gap-2">
                                  <span className={`px-2 py-1 text-xs rounded-full border ${getChangeTypeColor(change.type)}`}>
                                    {change.type}
                                  </span>
                                  <span className={`text-xs ${getSignificanceColor(change.significance)}`}>
                                    {change.significance} impact
                                  </span>
                                </div>
                              </div>

                              {change.type === 'modified' && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <p className="text-xs text-red-300 mb-1">Old Value:</p>
                                    <pre className="text-xs bg-red-500/10 border border-red-500/20 rounded p-2 overflow-x-auto">
                                      {formatValue(change.oldValue)}
                                    </pre>
                                  </div>
                                  <div>
                                    <p className="text-xs text-green-300 mb-1">New Value:</p>
                                    <pre className="text-xs bg-green-500/10 border border-green-500/20 rounded p-2 overflow-x-auto">
                                      {formatValue(change.newValue)}
                                    </pre>
                                  </div>
                                </div>
                              )}

                              {change.type === 'added' && (
                                <div>
                                  <p className="text-xs text-green-300 mb-1">Added Value:</p>
                                  <pre className="text-xs bg-green-500/10 border border-green-500/20 rounded p-2 overflow-x-auto">
                                    {formatValue(change.newValue)}
                                  </pre>
                                </div>
                              )}

                              {change.type === 'removed' && (
                                <div>
                                  <p className="text-xs text-red-300 mb-1">Removed Value:</p>
                                  <pre className="text-xs bg-red-500/10 border border-red-500/20 rounded p-2 overflow-x-auto">
                                    {formatValue(change.oldValue)}
                                  </pre>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <GitCompare className="w-12 h-12 mx-auto mb-4 text-gray-500" />
                  <p className="text-gray-400">No differences found between these versions</p>
                </div>
              )}

              {/* Metadata */}
              {comparison.metadata && (
                <div className="bg-zinc-700 rounded-lg p-4">
                  <h3 className="font-medium text-white mb-3">Comparison Metadata</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Algorithm:</span>
                      <span className="text-white ml-2">{comparison.metadata.algorithm}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Processing Time:</span>
                      <span className="text-white ml-2">{comparison.metadata.processingTime}ms</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Compared At:</span>
                      <span className="text-white ml-2">
                        {new Date(comparison.metadata.comparedAt).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : null}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-zinc-700">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
